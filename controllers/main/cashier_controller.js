"use strict";
define(['app','policy','transact','booklet','billings','api','atomic/bomb', 'policy/pay_d_monthly_2025', 'policy/pay_a_cash'],function(app,policy,TRNX,BKLT,billings, api, atomic, PayDMonthly2025, PayACash){
	const DATE_FORMAT = "yyyy-MM-dd";
	const TRNX_LIST = TRNX.__LIST;
	const BILL_MONTHS = billings.generateBillingMonths('BILL');
	const LAST_BILL_MO = BILL_MONTHS.length-1;
	// Use the last billing month from the BILLING object
	const LAST_BILL_MONTH = billings.getLastBillingMonthBILL();
	let NEXT_SY = false;

	// Grade level progression mapping
	const NEXT_LEVEL_MAP = {
		'NS': 'PR',  // Nursery -> Prep
		'PR': 'KN',  // Prep -> Kinder
		'KN': 'G1',  // Kinder -> Grade 1
		'G1': 'G2',  // Grade 1 -> Grade 2
		'G2': 'G3',  // Grade 2 -> Grade 3
		'G3': 'G4',  // Grade 3 -> Grade 4
		'G4': 'G5',  // Grade 4 -> Grade 5
		'G5': 'G6',  // Grade 5 -> Grade 6
		'G6': 'G7'   // Grade 6 -> Grade 7
	};
	app.register.controller('CashierController',['$scope','$rootScope','$filter','$timeout','api','aModal','Atomic',
	function($scope,$rootScope,$filter,$timeout,api,aModal,atomic){
		const $selfScope =  $scope;
		$scope = this;

		$scope.init = function(){
			$rootScope.$watch('_APP',function(app){
				if(!app) return;
				$scope.ActiveSY = $rootScope._APP.ACTIVE_SY;
				console.log($rootScope._APP);
				NEXT_SY = $rootScope._APP.MOD_ESP;
			});

			atomic.ready(function(){
				var sys = atomic.SchoolYears;
					sys = $filter('orderBy')(sys,'-id');
					sys = [sys[0]];
				var sy = atomic.ActiveSY;
				if(NEXT_SY){
					var asy = sy +1;
					var nsy ={};
						nsy.id =  asy;
						nsy.label = asy + '-'+ (asy+1);
						nsy.code =  (asy+''.substring(2));
					sys.push(nsy);
				}


				$scope.SchoolYears = $filter('orderBy')(sys,'-id');
				$scope.ActiveSY = sy;

			});
			$scope.Headers = ['Description',{class:'col-md-4',label:'Amount'}];
			$scope.Props = ['description','disp_amount'];
			$scope.Inputs = [{field:'description',disabled:true, enableIf:'OTHRS'},{field:'amount',type:'number'}];
			$scope.PSTypes = [
				{id:'regular', name:'Monthly Payment'},
				{id:'cash', name:'Full Payment'},
				{id:'old', name:'Ext. Payment Plan'}
			];
			$rootScope.__PSType = 'regular';
			$scope.PSType = 'regular';
			$rootScope.hasMultiplePS = false;
			$scope.PSHeaders = ['Due Date', 'Due Amount','Status'];
			$scope.PSProps = ['disp_date','disp_amount','disp_status'];
			$scope.Paysched = [];
			$scope.StudFields = ['id','full_name','enroll_status','student_type','department_id','year_level_id','section','sno'];
			$scope.StudSearch = ['first_name','last_name','middle_name','sno','rfid'];
			$scope.BarSearch = ['sno','ref_no','rfid'];
			$scope.StudDisplay = 'display_name';
			$scope.OthrFields = ['id','account_details','account_type'];
			$scope.OthrSearch = ['id','account_details'];
			$scope.OthrDisplay = 'account_details';
			$scope.OthrFilter = {account_type:"others"};
			$scope.TransacDetails=[];
			$scope.TotalAmount = 0;
			$scope.SeriesNo = '';
			$scope.PayeeType='STU';
			$scope.TransacDate = new Date();
			$scope.BillingMonths =BILL_MONTHS;
			$scope.BillMonth = BILL_MONTHS[LAST_BILL_MO].name.replace(' ','');
			$scope.TotalDispAmount = TRNX.util.formatMoney($scope.TotalAmount);
			if(!$scope.ActiveSY && atomic.ActiveSY)
				atomic.fuse();
		}
		$scope.editTrnxDetails = function(){
			$scope.TrnxDtlMode = 'EDIT';
			$scope.allowUpdate = true;
			$scope.allowPay = false;
			$scope.EditTransacDetails = angular.copy($scope.TransacDetails);
		}
		$scope.clearTrnxDetails = function(){
			let asy = angular.copy(atomic.ActiveSY);
			$scope.ActiveSY = asy;
			$scope.TrnxDtlMode = 'RESET';
			$selfScope.$broadcast('ResetTransactions');

		}
		$scope.closeTrnxDetails = function(){
			$scope.TrnxDtlMode = 'VIEW';
			$scope.allowUpdate = false;
		}
		$scope.updateTrnxDetails = function(){
			$scope.TrnxDtlMode = 'VIEW';
			$scope.allowPay = true;
			let trnxDetails = $scope.EditTransacDetails;
			$selfScope.$emit('UpdateTransacDetails',{details:trnxDetails,format:true});
			$selfScope.$broadcast('UpdateTransacList',{details:trnxDetails});
		}
		$scope.updateDetails = function(details){
			let hasZero = false;
			details.map(function(dtl){
				if(dtl.amount==0)
					hasZero=true;
			});
			if(hasZero) return;
			$scope.EditTransacDetails = details;
		}

		$selfScope.$watchGroup(['CAC.ActiveOther.id','CAC.ActiveStudent.id'],function(entity){
			$scope.hasValidPayee = entity[0] ||  entity[1];
			if(!$scope.hasValidPayee){
				let asy = angular.copy(atomic.ActiveSY);
				$scope.ActiveSY = asy;
			}
		});
		$selfScope.$watch('CAC.PayeeType',function(){
			let asy = angular.copy(atomic.ActiveSY);
				$scope.ActiveSY = asy;
		});
		$selfScope.$watchGroup(['CAC.ActiveOther','CAC.ActiveSY','CAC.ActiveOtherId'],function(entity){
			var othr = entity[0];
			var sy = entity[1];
			var oid = entity[2];
			if(!othr||!sy) return;
			if(oid && !othr.id){
				let name = othr.name || othr;
				othr = {id:oid,name:name};
				$scope.ActiveOther = othr;
				$scope.ActiveOtherId = null;
				othr.account_details = name;

			}
			if(!othr.id)
				resetTransactionUI();

			$selfScope.$broadcast('OtherSelected',{other:othr,sy:sy});
		});
		$selfScope.$watchGroup(['CAC.ActiveStudent','CAC.ActiveSY'],function(entity){
			var stud = entity[0];
			var sy = entity[1];
			if(!stud||!sy) return;
			if(!stud.id){
				resetTransactionUI();
			}

			let asy = atomic.ActiveSY;
			if(NEXT_SY && sy<asy+1 && stud.enroll_status=='NEW'){
				$scope.ActiveSY = asy+1;
			}
			if(stud.sno.startsWith(asy+1)&& false){
				$scope.ActiveSY = asy+1;
			}

			if(NEXT_SY&&sy<asy+1){
				let yid = stud.year_level_id;
				let incomingLevel = NEXT_LEVEL_MAP[yid] || yid;
				TRNX.generateMonthlySched(stud.id, incomingLevel, $scope.ActiveSY);
				$scope.PSType = 'regular';
				$rootScope.__PSType = 'regular';
				let regularSched = TRNX.getSched('regular');
				$selfScope.$emit('UpdatePaysched', {paysched: regularSched});
			}

			$selfScope.$broadcast('StudentSelected',{student:stud,sy:sy});
		});
		$selfScope.$watch('CAC.TotalAmount',function(amount){
			let isValid = $scope.hasValidPayee;
			if(!isValid) return;
			$scope.allowPay = isValid && $scope.ActiveSY && amount>0;
			$scope.allowClear = $scope.allowPay && $scope.TrnxDtlMode!=='EDIT';
			$scope.allowEdit = $scope.allowPay && $scope.TrnxDtlMode!=='EDIT';
		});

		$selfScope.$on('UpdatePaysched',function(evt,args){
			$scope.Paysched = args.paysched;
			// Set incoming level if SY is 2025
			if ($scope.ActiveSY === 2025 && $scope.ActiveStudent && $scope.ActiveStudent.year_level_id) {
				let yid = $scope.ActiveStudent.year_level_id;
				let incomingLevelCode = NEXT_LEVEL_MAP[yid] || yid;

				// Create a more descriptive incoming level name
				let incomingLevelName = '';
				if (incomingLevelCode === 'NS') {
					incomingLevelName = 'Nursery';
				} else if (incomingLevelCode === 'PR') {
					incomingLevelName = 'Prep';
				} else if (incomingLevelCode === 'KN') {
					incomingLevelName = 'Kinder';
				} else if (incomingLevelCode.startsWith('G')) {
					// For grade levels (G1, G2, etc.), extract the number and add "Grade"
					let gradeNumber = incomingLevelCode.substring(1);
					incomingLevelName = 'Grade ' + gradeNumber;
				} else {
					// Default to the code if it doesn't match any pattern
					incomingLevelName = incomingLevelCode;
				}

				// Store both the code and the name
				$scope.IncomingLevel = incomingLevelCode;
				$scope.IncomingLevelName = incomingLevelName;
			} else {
				$scope.IncomingLevel = null;
				$scope.IncomingLevelName = null;
			}
			// Create optimized JSON string of payment schedule for SOA preview
			if (args.paysched && args.paysched.length > 0) {
				// Create a more compact representation with only the necessary fields
				const compactPaysched = {
					transaction_type_ids: [],
					due_dates: [],
					amounts: []
				};
				const compactLegder ={};

				// Extract only the needed fields from each payment schedule item
				args.paysched.forEach(function(item) {
					// Add transaction type ID
					compactPaysched.transaction_type_ids.push(item.transaction_type_id);

					// Format the due date as YYYY-MM-DD
					let formattedDate;
					if (item.transaction_type_id === 'REGFE') {
						// For registration fee, use the current date
						formattedDate = new Date().toISOString().split('T')[0];
					} else if (item.transaction_type_id === 'INIPY' || item.transaction_type_id === 'FULPY') {
						// For initial payment or full payment, use a week from now
						const oneWeekFromNow = new Date();
						oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);
						formattedDate = oneWeekFromNow.toISOString().split('T')[0];
					} else if (item.due_date instanceof Date) {
						formattedDate = item.due_date.toISOString().split('T')[0]; // Get YYYY-MM-DD part
					} else if (typeof item.due_date === 'string') {
						// Check if it's already a date string
						if (item.due_date.includes('T')) {
							// It's an ISO string, extract the date part
							formattedDate = item.due_date.split('T')[0];
						} else if (/^\d{4}-\d{2}-\d{2}$/.test(item.due_date)) {
							// It's already in YYYY-MM-DD format
							formattedDate = item.due_date;
						} else if (item.due_date === 'Registration' || item.due_date === 'Upon Enrollment') {
							// For special text dates, use appropriate dates
							if (item.due_date === 'Registration') {
								formattedDate = new Date().toISOString().split('T')[0];
							} else {
								const oneWeekFromNow = new Date();
								oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);
								formattedDate = oneWeekFromNow.toISOString().split('T')[0];
							}
						} else {
							// Try to parse and format the date
							try {
								formattedDate = new Date(item.due_date).toISOString().split('T')[0];
							} catch (e) {
								// If parsing fails, use the current date
								formattedDate = new Date().toISOString().split('T')[0];
							}
						}
					} else {
						// Default to current date if all else fails
						formattedDate = new Date().toISOString().split('T')[0];
					}

					compactPaysched.due_dates.push(formattedDate);

					// Add amount (ensure it's a number)
					let amount = item.due_amount;
					if (typeof amount === 'string') {
						// Remove commas and convert to number
						amount = parseFloat(amount.replace(/,/g, ''));
					}
					compactPaysched.amounts.push(amount);
				});

				// Add metadata for due_now calculation
				compactPaysched.due_now_date = '2025-05-30'; // May 30, 2025

				// Add indices of REGFE and INIPY in the payment schedule
				compactPaysched.bill_month_indices = [];

				// Add fee breakdown for precomputed ledger
				if ($scope.ActiveSY === 2025 && $scope.IncomingLevel) {
					let incomingLevel = $scope.IncomingLevel;
					if (PayDMonthly2025.PLAN_D_MONTHLY[incomingLevel]) {
						const feeData = PayDMonthly2025.PLAN_D_MONTHLY[incomingLevel];
						compactLegder.REGFE = feeData.registration_fee;
						compactLegder.ANNSF = feeData.annual_school_fee;
						compactLegder.TUIXN = feeData.tuition_fee;
						compactLegder.ENERF = feeData.energy_fee;
						compactLegder.LERMA = feeData.learning_materials;
					}
				}

				// Extract indices of REGFE and INIPY in the payment schedule
				args.paysched.forEach(function(item, index) {
					if (item.transaction_type_id === 'REGFE' || item.transaction_type_id === 'INIPY') {
						// Add the index of this item in the payment schedule
						compactPaysched.bill_month_indices.push(index);
					}
				});

				$scope.PayschedJSON = JSON.stringify(compactPaysched);
				$scope.LedgerJSON = JSON.stringify(compactLegder);

				// Store in rootScope for access by the modal controller
				$rootScope.CAC = $rootScope.CAC || {};
				$rootScope.CAC.PayschedJSON = $scope.PayschedJSON;
				$rootScope.CAC.LedgerJSON = $scope.LedgerJSON;
				$rootScope.CAC.IncomingLevel = $scope.IncomingLevel;
				$rootScope.CAC.IncomingLevelName = $scope.IncomingLevelName;

			} else {
				$scope.PayschedJSON = null;
				$scope.IncomingLevel = null;
				$scope.LedgerJSON = null;

				// Clear from rootScope
				if ($rootScope.CAC) {
					$rootScope.CAC.PayschedJSON = null;
					$rootScope.CAC.LedgerJSON = null;
					$rootScope.CAC.IncomingLevel = null;
					$rootScope.CAC.IncomingLevelName = null;
				}
			}
		});
		$selfScope.$on('UpdateTransacDetails',function(evt,args){
			let details = args.details;
			let formatAmount = args.format;
			let totalAmt = 0;
			$scope.TransacDetails = details;
			details.map(function(item,index){
				totalAmt+=item.amount;
				if(formatAmount)
					item.disp_amount = TRNX.util.formatMoney(item.amount);
			});
			$scope.TotalAmount = totalAmt;
			$scope.TotalDispAmount = TRNX.util.formatMoney(totalAmt);
		});

		$scope.openPaymentModal = function(){
			$selfScope.$broadcast('OpenPayModal',{total_amount:$scope.TotalAmount,total_amount_display:$scope.TotalDispAmount,details:$scope.TransacDetails});
		}

		$selfScope.$on('PaymentSucess',function(evt,args){
			let account_type = args.details.account_type;
			let account = args.details.account;
			if(account_type=='others'){
				let othr = $scope.ActiveOther;
				let sy = $scope.ActiveSY;
				$selfScope.$broadcast('ResetTransactions');
				$timeout(function(){
					$selfScope.$broadcast('OtherSelected',{other:othr,sy:sy});
					$scope.ActiveTabIndex = 0;
				},1500);
			}else{

				let stud = $scope.ActiveStudent;
				let sy = $scope.ActiveSY;
				$selfScope.$broadcast('ResetTransactions');
				$timeout(function(){
					if(account.hasOwnProperty('account_id')){
						stud.id = account.account_id;
						stud.enroll_status = 'OLD';
					}
					$selfScope.$broadcast('StudentSelected',{student:stud,sy:sy});
					$scope.ActiveTabIndex = 1;
				},1500);
			}
			$selfScope.$broadcast('ClosePayModal');
			$selfScope.$broadcast('PrintPaymentReceipt',{details:args.details});
		});
		$selfScope.$on('PaymentError',function(evt,args){

			alert(args.message);
		});
		$selfScope.$on('PrintPaymentReceipt',function(evt,args){
			$scope.PrintPaymentDetails = args.details;
			let docType = $scope.PrintPaymentDetails.doc_type;
			let payForm =  `Print${docType}Payment`;

			$timeout(function(){
				document.getElementById(payForm).submit();
			},200);
		});

		function resetTransactionUI(){
			$selfScope.$broadcast('UpdatePaysched',{paysched:[]});
			$selfScope.$broadcast('ResetTransactions');

		}
	}]);

	app.register.controller('CashierTransactionsController',['$scope','$rootScope','$filter','$timeout','api','Atomic',
	function($scope,$rootScope,$filter,$timeout,api,atomic){
		const $selfScope =  $scope;
		$scope = this;
		$scope.init = function(){
			TRNX.runDefault();
			$scope.TransacList = TRNX.getList();
			TRNX.link(api);
			$scope.ActiveTrnx = {};
			$scope.PSType = 'regular'; // Default payment schedule type
		};

		$scope.addTrnx = function(id){
			// Toggle the selected transaction without clearing others
			$scope.ActiveTrnx[id] = !$scope.ActiveTrnx[id];

			// Add validation for Full Payment selection
			if (id === 'FULPY' && $scope.ActiveTrnx[id]) {
				// If Full Payment is selected, remove Registration Fee and Upon Enrollment
				$scope.ActiveTrnx['REGFE'] = false;
				$scope.ActiveTrnx['INIPY'] = false;

				// Also disable Registration Fee and Upon Enrollment
				$scope.TransacList.forEach(function(item, index) {
					if (item.id === 'REGFE' || item.id === 'INIPY') {
						$scope.TransacList[index].disabled = true;
					}
				});

				// Generate and display cash payment schedule
				if ($scope.ActiveStudent && $scope.ActiveStudent.id) {
					// Get the student's incoming year level
					let yid = $scope.ActiveStudent.year_level_id;
					let incomingLevel = NEXT_LEVEL_MAP[yid] || yid;

					// Generate cash payment schedule
					TRNX.generateCashSched($scope.ActiveStudent.id, incomingLevel, $scope.ActiveSY);

					// Update the schedule type to 'cash'
					$scope.PSType = 'cash';
					$rootScope.__PSType = 'cash';

					// Update the payment schedule display
					let cashSched = TRNX.getSched('cash');
					$selfScope.$emit('UpdatePaysched', {paysched: cashSched});
				}
			} else if ((id === 'REGFE' || id === 'INIPY') && $scope.ActiveTrnx[id]) {
				// If Registration Fee or Upon Enrollment is selected, remove Full Payment
				$scope.ActiveTrnx['FULPY'] = false;

				// Also disable Full Payment
				$scope.TransacList.forEach(function(item, index) {
					if (item.id === 'FULPY') {
						$scope.TransacList[index].disabled = true;
					}
				});
				// Get the student's incoming year level
				let yid = $scope.ActiveStudent.year_level_id;
				let incomingLevel = NEXT_LEVEL_MAP[yid] || yid;

				// Generate monthly payment schedule
				TRNX.generateMonthlySched($scope.ActiveStudent.id, incomingLevel, $scope.ActiveSY);

				// Ensure we're using the regular payment schedule
				$scope.PSType = 'regular';
				$rootScope.__PSType = 'regular';

				// Get the current regular payment schedule
				let regularSched = TRNX.getSched('regular');

				// If the regular schedule is empty, try to load it
				if (!regularSched || regularSched.length === 0) {
					if ($scope.ActiveStudent && $scope.ActiveStudent.id) {
						// Force a reload of the payment schedule
						let sid = $scope.ActiveStudent.id;
						let sy = $scope.ActiveSY;

						// This will trigger the payment schedule to be loaded
						TRNX.getAccount(sid, sy).then(function(response) {
							var account = response.data.data[0];
							var sched = TRNX.getSched('regular');
							$selfScope.$emit('UpdatePaysched', {paysched: sched});
						});
					}
				} else {
					// Update the payment schedule display with existing data
					$selfScope.$emit('UpdatePaysched', {paysched: regularSched});
				}
			} else if (id === 'FULPY' && !$scope.ActiveTrnx[id]) {
				// If Full Payment is deselected, enable Registration Fee and Upon Enrollment
				$scope.TransacList.forEach(function(item, index) {
					if (item.id === 'REGFE' || item.id === 'INIPY') {
						delete $scope.TransacList[index].disabled;
					}
				});

				// Switch back to regular payment schedule
				if ($scope.PSType === 'cash') {
					$scope.PSType = 'regular';
					$rootScope.__PSType = 'regular';

					// Update the payment schedule display
					let regularSched = TRNX.getSched('regular');
					$selfScope.$emit('UpdatePaysched', {paysched: regularSched});
				}
			} else if ((id === 'REGFE' || id === 'INIPY') && !$scope.ActiveTrnx[id]) {
				// Check if both Registration Fee and Upon Enrollment are deselected
				if (!$scope.ActiveTrnx['REGFE'] && !$scope.ActiveTrnx['INIPY']) {
					// Enable Full Payment
					$scope.TransacList.forEach(function(item, index) {
						if (item.id === 'FULPY') {
							delete $scope.TransacList[index].disabled;
						}
					});
				}
			}

			updateTrnxUI();
			let activeTrnx = $filter('filter')($scope.TransacList,{isActive:true});
			$selfScope.$emit('UpdateTransacDetails',{details:activeTrnx});
		}
		$selfScope.$on('ResetTransactions',function(evt,args){
			$scope.ActiveTrnx={};
			updateTrnxUI();
			$scope.TransacList=TRNX.getList();
			$selfScope.$emit('UpdateTransacDetails',{details:[]});
			$scope.PSType = null;
			$rootScope.__PSType =null;
		});

		$selfScope.$on('UpdateTransacList',function(evt,args){
			let details = args.details;
			details.map(function(dtl){
				$scope.TransacList.map(function(trl,index){
					if(dtl.id===trl.id){
						trl.amount = dtl.amount;
						trl.disp_amount = dtl.disp_amount;
						$scope.TransacList[index]=trl;
					}
				});
			});

		});
		function updateTrnxUI(){
			$scope.TransacList.map(function(item,index){
				$scope.TransacList[index].isActive = !!$scope.ActiveTrnx[item.id];
			});
		}
		$selfScope.$on('OtherSelected',function(evt,args){
			$scope.ActiveTrnx = {};
			let OTH = args.other;
			let oid = OTH.id;
			let sy = args.sy;
			if(!oid) return;
			$scope.loadingTrnx = true;
			TRNX.runDefault();
			$selfScope.$emit('UpdatePaysched',{paysched:[]});

			function updateTrnx(response){
				var account =  response.data.data[0];
				$scope.TransacList = angular.copy(TRNX.getList());
				$scope.loadingTrnx = false;
			}
			TRNX.getAccount(oid,sy).then(updateTrnx);

		});
		$selfScope.$on('StudentSelected',function(evt,args){
			$scope.ActiveTrnx = {};

			var STU =  args.student;
			var sy = args.sy;
			var sid = STU.id;
			var type =  STU.enroll_status;
			var asy = atomic.ActiveSY;

			// Store the active student and SY for later use
			$scope.ActiveStudent = STU;
			$scope.ActiveSY = sy;

			if(!sid) return;
			$scope.loadingTrnx = true;
			TRNX.runDefault();



			// Add Registration Fee, Upon Enrollment, and Full Payment for SY 2025-2026
			if (sy === 2025 ) {
				// Determine incoming year level
				let yid = $scope.ActiveStudent.year_level_id;
				let incomingLevel = NEXT_LEVEL_MAP[yid] || yid;

				// Get fee information from pay_d_monthly_2025.js and pay_a_cash.js
				let regFee = 0;
				let uponEnrollmentFee = 0;
				let fullPaymentFee = 0;

				// Get monthly payment details
				if (PayDMonthly2025.PLAN_D_MONTHLY[incomingLevel]) {
					const feeData = PayDMonthly2025.PLAN_D_MONTHLY[incomingLevel];
					regFee = feeData.registration_fee;

					// Get the Upon Enrollment amount (first amount in due_amounts)
					if (feeData.due_amounts && feeData.due_amounts.length > 0) {
						uponEnrollmentFee = feeData.due_amounts[0];
					}
				}

				// Get full payment details
				if (PayACash.PLAN_A_CASH_BASIS[incomingLevel]) {
					const cashFeeData = PayACash.PLAN_A_CASH_BASIS[incomingLevel];
					fullPaymentFee = cashFeeData.total_due;
				}

				// Update the registration fee in the transaction list
				if (regFee > 0) {
					TRNX.updateAmount('REGFE', 'set', regFee);
					TRNX.updateDisplay('REGFE', 'show');
				}

				// Add Upon Enrollment fee
				if (uponEnrollmentFee > 0) {
					TRNX.updateAmount('INIPY', 'set', uponEnrollmentFee);
					TRNX.updateDisplay('INIPY', 'show');
				}

				// Add Full Payment fee
				if (fullPaymentFee > 0) {
					TRNX.updateAmount('FULPY', 'set', fullPaymentFee);
					TRNX.updateDisplay('FULPY', 'show');
				}

				// Make sure the transaction options are properly enabled/disabled
				// by default (all enabled initially)
				$scope.TransacList.forEach(function(item, index) {
					if (item.id === 'REGFE' || item.id === 'INIPY' || item.id === 'FULPY') {
						delete $scope.TransacList[index].disabled;
					}
				});

				TRNX.updateAmount('INRES','set',0);
				TRNX.updateAmount('RECOG','set',0);
				TRNX.updateAmount('MVEUP','set',0);
				TRNX.updateDisplay('INRES','hide');
				TRNX.updateDisplay('RECOG','hide');
				TRNX.updateDisplay('MVEUP','hide');
				TRNX.updateDisplay('OTHRS','enable');

				// Generate monthly payment schedule
				TRNX.generateMonthlySched($scope.ActiveStudent.id, incomingLevel, $scope.ActiveSY);
				let regularSched =  TRNX.getSched('regular');

				$rootScope.__PSType = 'regular';
				$rootScope.hasMultiplePS = false;

				$selfScope.$emit('UpdatePaysched',{paysched:regularSched});

			}else{
				// Load Updated Moving UP Fee
				let studentYearLevel = $scope.ActiveStudent.year_level_id;
				let feeAmount = policy.getFeeAmount('MVEUP', studentYearLevel);
				if(feeAmount){
					TRNX.updateAmount('MVEUP', 'set', feeAmount);
					TRNX.updateAmount('RECOG', 'set', feeAmount);
					let isMovingUp = studentYearLevel=='G6' || studentYearLevel=='KN';
					if(isMovingUp){
						TRNX.updateAmount('RECOG','set',0);
					}else{
						TRNX.updateAmount('MVEUP','set',0);
					}
				}
			}

			$scope.TransacList = angular.copy(TRNX.getList());



			// Don't clear the payment schedule here, as we'll load it properly in updateTrnx

			if(!sid) return;

			var loadNextSY = NEXT_SY && sy> asy;
			var hasOldAccount = false;

			function updateTrnx(response){

				var account =  response.data.data[0];
				var type =  account.hasOwnProperty('Paysched')?'regular':'old';
				var sched =  TRNX.getSched(type);
				if(sched.length)
				$selfScope.$emit('UpdatePaysched',{paysched:sched});
				if(type=='old' &&sched.length>1)
					hasOldAccount = true;

				let specialPay = [];
				$scope.TransacList = angular.copy(TRNX.getList());
				$scope.TransacList.map(function(trl){
					let ispayment = /^(REGFE|EXTPY|SBQPY)$/.test(trl.id) && trl.amount>0;
					if (ispayment)
						specialPay.push(trl.id);
				});
				$scope.PSType = type;
				$rootScope.__PSType = type;
				$rootScope.hasMultiplePS =  specialPay.length>1 || hasOldAccount;
				$scope.loadingTrnx = false;
				$selfScope.$emit('DisplaySOA');
			}

			function loadCurrentAccount(){
				if(STU.enroll_status=='NEW'){
					$timeout(function(){
						$scope.loadingTrnx = false;
						$scope.hasMultiplePS = false;
					},300);
					return;
				}
				TRNX.getOldAccount(sid,sy).then(updateTrnx).finally(function(){
					$timeout(function(){
						TRNX.getAccount(sid,sy).then(updateTrnx);
						$scope.loadingTrnx = false;
						$selfScope.$emit('DisplaySOA');
					},300);

				});

			}

			if(loadNextSY){
				TRNX.getAssessment(sid,sy).then(updateTrnx).finally(function(){
					$scope.loadingTrnx = false;

					//TRNX.updateAmount('INRES','set',3000);
					//TRNX.updateDisplay('INRES','show');

					$scope.TransacList = angular.copy(TRNX.getList());
					return loadCurrentAccount(sid,sy);
				});
			}

			return loadCurrentAccount(sid,sy);



		});
		$selfScope.$watch('CAC.PSType',function(type){
			var sched =  TRNX.getSched(type);
			$selfScope.$emit('UpdatePaysched',{paysched:sched});
			$selfScope.$emit('DisplaySOA');
			$rootScope.__PSType =type;
		});
		$selfScope.$on('DisplaySOA',function(){
			$timeout(function(){
				let formId =  'PrintPPSoa2';
				let sy = $scope.ActiveSY;
				if(sy==2025 )
					formId =  'PrintSOAPreview';
				document.getElementById(formId).submit();
			},200);
		});

	}]);


	app.register.controller('CashierModalController',['$scope','$rootScope','$filter','$timeout','api','aModal',
	function($scope,$rootScope,$filter,$timeout,api,aModal){
		const $selfScope =  $scope;
		$scope = this;
		$scope.init = function(){
			BKLT.link(api);
			$scope.PayObj = {};
			$scope.DocTypes = [
					{id:"OR", name:"Sales Invoice"},
					{id:"AR", name:"Acknowlegment Receipt"},
					//{id:"A2O", name:"A2O"},
				];
			$scope.PayTypes = [
					{id:"CASH",name:"Cash"},
					{id:"CHCK",name:"Check"},
					{id:"GCSH",name:"GCash"},
					{id:"MAYA",name:"Maya"},
					{id:"BBDO",name:"BDO"},
					{id:"BBPI",name:"BPI"},
					//{id:"CARD",name:"Card"}
				];


		}
		$selfScope.$on('OpenPayModal',function(evt,args){
			aModal.open('CashierPaymentModal');

			// Determine document type based on selected transactions
			// If multiple transactions are selected, use the most common docType
			// If there's a tie, prioritize in order: OR, AR, A2O
			let docTypes = {};
			let defaultDocType = 'A2O';

			// Count occurrences of each docType
			args.details.forEach(function(detail) {
				let type = detail.docType || 'A2O';
				docTypes[type] = (docTypes[type] || 0) + 1;
			});

			// Find the most common docType
			let maxCount = 0;
			let docTypePriority = ['OR', 'AR', 'A2O'];

			// First check if there's a clear winner by count
			Object.keys(docTypes).forEach(function(type) {
				if (docTypes[type] > maxCount) {
					maxCount = docTypes[type];
					defaultDocType = type;
				}
			});

			// If there's a tie, use priority order
			if (Object.keys(docTypes).filter(type => docTypes[type] === maxCount).length > 1) {
				for (let i = 0; i < docTypePriority.length; i++) {
					if (docTypes[docTypePriority[i]] === maxCount) {
						defaultDocType = docTypePriority[i];
						break;
					}
				}
			}

			$scope.PayObj.series_no = null;
			$scope.PayObj.doc_type = defaultDocType;
			$scope.PayObj.pay_type = 'CASH';
			$scope.PayObj.transac_date = new Date();
			$scope.PayObj.transac_date_display = $filter('date')(new Date(),'dd MMM yyyy');
			$scope.PayObj.pay_due = args.total_amount
			$scope.PayObj.pay_amount = args.total_amount;
			$scope.PayObj.pay_display = args.total_amount_display;
			$scope.PayObj.details = args.details;
			$scope.PayObj = distributePayment($scope.PayObj);
			$scope.isPayObjValid = false;
		});

		function distributePayment(payObj) {
		    let remainingAmount = payObj.pay_amount; // Total amount to be paid

		    // Handle multiple transactions
		    if (payObj.details.length > 1) {
		        // For multiple transactions, we don't need to distribute the payment
		        // Each transaction already has its own amount
		        return payObj;
		    }

		    // Handle single transaction with breakdown (original behavior)
		    let breakdown = payObj.details[0].breakdown;
		    if (!breakdown) return payObj;

		    let distributedBreakdown = {};

		    // Deduct from SBQPY first
		    if (breakdown.SBQPY && remainingAmount > 0) {
		        let deduction = Math.min(breakdown.SBQPY, remainingAmount);
		        distributedBreakdown.SBQPY = Number(deduction.toFixed(2));
		        remainingAmount -= deduction;
		    }

		    // Deduct from ACEC breakdowns
		    let keys = Object.keys(breakdown).filter(k => k !== 'SBQPY');
		    for (let key of keys) {
		        if (remainingAmount <= 0) break;

		        let deduction = Math.min(breakdown[key], remainingAmount);
		        distributedBreakdown[key] = Number(deduction.toFixed(2));
		        remainingAmount -= deduction;
		    }

		    // Handle excess payment (if pay_amount exceeds total SBQPY + ACEC)
		    if (remainingAmount > 0) {
		        distributedBreakdown.SBQPY = (distributedBreakdown.SBQPY || 0) + Number(remainingAmount.toFixed(2));
		    }
		    payObj.details[0].breakdown = distributedBreakdown;
		    return payObj;
		}

		$selfScope.$on('ClosePayModal',function(evt,args){
			$scope.PayObj.series_no =null;
			$scope.PayObj.doc_type =null;
			$scope.PayObj.pay_due =null;
			$scope.PayObj.pay_amount =null;
			$scope.PayObj.pay_display =null;
			$scope.PayObj.pay_details =null;
			$scope.PayObj.pay_date =null;
			$scope.PayObj.transac_date =null;
			$scope.PayObj.transac_date_display =null;
			$scope.PayObj.details =null;
			aModal.close('CashierPaymentModal');
		});
		$selfScope.$watch('CMC.PayObj.doc_type',loadBooklet);
		$selfScope.$watchGroup(['CMC.PayObj.series_no','CMC.isCheckingSeries','CMC.PayObj.pay_type','CMC.PayObj.pay_details','CMC.PayObj.pay_date'],function(values){
			$scope.isPayObjValid  = $scope.PayObj.series_no!=null && !$scope.isCheckingSeries;
			if(values[2]=='CHCK'){
				let checkDetails = $scope.PayObj.pay_details && $scope.PayObj.pay_date;
				$scope.isPayObjValid  =  $scope.isPayObjValid  && checkDetails
			}
		});
		$selfScope.$on('OtherSelected',function(evt,args){
			$scope.PayObj.other = args.other.account_details;
			$scope.PayObj.account_id = args.other.id;
			$scope.PayObj.account_type = 'others';
			$scope.PayObj.esp = args.sy;
		});
		$selfScope.$on('StudentSelected',function(evt,args){
			$scope.PayObj.student = args.student.name;
			$scope.PayObj.section = args.student.section;
			$scope.PayObj.account_id = args.student.id;
			$scope.PayObj.account_type = 'student';
			$scope.PayObj.esp = args.sy;

		});
		$selfScope.$watch('CMC.PayObj.pay_amount',function(amt){
			let pay_due = $scope.PayObj.pay_due;
			let pay_change = amt - pay_due;
			$scope.PayObj.pay_change = pay_change;
		});
		$scope.closeModal = function(){
			$selfScope.$emit('ClosePayModal');
		}

		$scope.confirmPayment = function(){
			let payment = angular.copy($scope.PayObj);
			payment.transac_date = $filter('date')(new Date(payment.transac_date),DATE_FORMAT);

			// Check if payment includes INIPY or FULPY transactions
			let hasInitialPayment = false;
			let hasFullPayment = false;

			if (payment.details && payment.details.length > 0) {
				payment.details.forEach(function(detail) {
					if (detail.id === 'INIPY') {
						hasInitialPayment = true;
					} else if (detail.id === 'FULPY') {
						hasFullPayment = true;
					}
				});
			}

			// If this is an INIPY or FULPY payment for SY 2025, add assessment data
			if ((hasInitialPayment || hasFullPayment) && payment.esp === 2025) {
				// Get the payment schedule and ledger data from the parent controller
				let paymentSchedule = null;
				let ledgerData = null;

				// Access the parent scope to get the PayschedJSON and LedgerJSON
				if ($rootScope.CAC && $rootScope.CAC.PayschedJSON) {
					try {
						paymentSchedule = JSON.parse($rootScope.CAC.PayschedJSON);
					} catch (e) {
						console.error('Error parsing payment schedule JSON:', e);
					}
				}

				if ($rootScope.CAC && $rootScope.CAC.LedgerJSON) {
					try {
						ledgerData = JSON.parse($rootScope.CAC.LedgerJSON);
					} catch (e) {
						console.error('Error parsing ledger JSON:', e);
					}
				}

				// Add assessment data to the payment object
				if (paymentSchedule || ledgerData) {
					payment.assessment = {
						paysched: paymentSchedule,
						fees: ledgerData,
						incoming_level: $rootScope.CAC ? $rootScope.CAC.IncomingLevel : null,
						incoming_level_name: $rootScope.CAC ? $rootScope.CAC.IncomingLevelName : null
					};

					// Log the assessment data for debugging
					console.log('Adding assessment data to payment:', payment.assessment);
				}
			}

			let success = function(response){
				let data = response.data;
				$selfScope.$emit('PaymentSucess',{details:data});
			};
			let error = function(response){
				$selfScope.$emit('PaymentError',{message:response.message});
			};

			api.POST('new_payments',payment,success,error);
		}

		function loadBooklet(){
			let doc_type = $scope.PayObj.doc_type;
			$scope.PayObj.series_no = 'Checking...';
			$scope.isCheckingSeries = true;
			$scope.isPayObjValid = false;
			$timeout(function(){
				BKLT.requestBooklets(doc_type).then(setDefaults,errDefaults);
			},350);
		}
		function setDefaults(){

			$scope.Booklets = BKLT.getBooklets();
			let bklt_id = $scope.Booklets[0].id;
			let active_BL = BKLT.setActiveBL(bklt_id);
			$scope.PayObj.booklet_id = bklt_id;
			let seriesNo = active_BL.series_no;
			if(active_BL.receipt_type=='OR')
				seriesNo = seriesNo.replace('OR','SI');
			$scope.PayObj.series_no = seriesNo;
			$timeout(function(){
				$scope.isCheckingSeries = false;
			},700);
		}
		function errDefaults(){
			$scope.isCheckingSeries = false;
			$scope.PayObj.series_no = null;
		}


	}]);


});