<style>
.tab-content{
	padding-left: 15px;
}
.btn-action{
	width: 220px;
	height:100px;
	text-align:left;
	margin-right: 10px;
	margin-bottom: 10px;

}
.btn-action>span.peso.amount{
	width: auto;
	float: none;
	color: inherit;
}
.btn-action div.action-label {
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 40px;
}
.btn-action:hover div.action-label{
	white-space: normal;
}
.btn-active,.btn-active:hover,.btn-active:focus,
.btn-active[disabled],.btn-active[disabled]:hover,.btn-active[disabled]:focus{
	border:1px solid #337ab7;
	color:#337ab7;
	background: white;
}
.btn-action.btn-active,
.btn-action.btn-active:hover,
.btn-action.btn-active:focus{
	background: #d9edf7;
	border:2px solid #337ab7;
}
.btn-active[disabled],
.btn-active[disabled]:hover,
.btn-active[disabled]:focus{
	background: white;
}
div[align="right"] input{
	text-align: right;
}
div[align="right"] input[disabled]{
	background: white;
    border: none;
    padding: 0;
    color: black;
    font-size: 21px;
    height: auto;
}
#PTypeSelect{
	width: 104px;
  margin-left: 15px;
  margin-right: -32px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  z-index: 3;
  position: absolute;
  font-size: 14px;
}
div.simple-typeahead{
	margin-left:20px;
}
</style>
<div ng-controller="CashierController as CAC" ng-init="CAC.init()">
	<a-module>
		<a-container>
			<a-header is-main="true">
				<a-row>
					<a-col size="1">
						<select  ng-model="CAC.PayeeType" class="input-lg pull-right" a-select id="PTypeSelect" ng-disabled="CAC.hasValidPayee">
							<option value="STU">Student</option>
							<!--<option value="BAR"> Scan</option>-->
							<option value="OTH"> Others</option>
						</select>
					</a-col>
					<a-col size="11">
						<m-search-entity  ng-show="CAC.PayeeType=='STU'" ng-model="CAC.ActiveStudent" endpoint="'students/search'" search-fields="CAC.StudSearch" obj-fields="CAC.StudFields" is-large="true" display-field="CAC.StudDisplay" placeholder="Search Students"></m-search-entity>

						<m-search-entity   ng-show="CAC.PayeeType=='OTH'" ng-model="CAC.ActiveOther" endpoint="'accounts'" search-fields="CAC.OthrSearch" obj-fields="CAC.OthrFields" is-large="true" display-field="CAC.OthrDisplay" placeholder="Search Other Accounts"  filter="CAC.OthrFilter" allow-create="true" created-id="CAC.ActiveOtherId"></m-search-entity>

						<m-search-entity  ng-show="CAC.PayeeType=='BAR'" ng-model="CAC.ActiveStudent" endpoint="'students/search'" search-fields="CAC.BarSearch" obj-fields="CAC.StudFields" is-large="true" display-field="CAC.StudDisplay" placeholder="Scan Barcode or RFID"></m-search-entity>

					</a-col>
				</a-row>

			</a-header>
			<a-canvas>
				<a-content>
					<a-row>
						<a-col size="8">
							<div class="pull-right" style="width:230px;">
								<m-formgroup ng-model="CAC.ActiveSY" label="School Year" options="CAC.SchoolYears" option-label="label"  select-prefix="Select School Year" ng-change="CAC.changeASY()"></m-formgroup>
							</div>
							<uib-tabset type="pills" active="CAC.ActiveTabIndex">
								<uib-tab index="0" heading="Transactions" classes="btn-lg">

									<div class="empty-state" ng-hide="CAC.hasValidPayee">
										<div class="well text-center">
											<div class="empty-state-icon text-center">
												<i  ng-if="CAC.PayeeType=='STU'"class="glyphicon glyphicon-user"></i>
												<i ng-if="CAC.PayeeType=='OTH'" class="glyphicon glyphicon-magnet"></i>
											</div>
											<h4>Select {{CAC.PayeeType=='STU'?'Student':'Other Account'}} to begin transaction.</h4>
											<p ng-if="CAC.PayeeType=='STU'">Search by Name, Student Number or tap their ID.</p>
											<p ng-if="CAC.PayeeType=='OTH'">Search by Account Name.</p>
										</div>
									</div>
										<div ng-controller="CashierTransactionsController as CTC" ng-init="CTC.init()" ng-show="CAC.hasValidPayee">

											<div class="loader fade in" ng-class="{'out':!CTC.loadingTrnx,'in':CTC.loadingTrnx}"></div>
											<!--- Transactions - allowed & defaults -->
											<a-button size="lg" class="btn-action" ng-class="{'btn-active':CTC.ActiveTrnx[TItem.id]}" ng-repeat="TItem in CTC.TransacList" ng-hide="TItem.hide" ng-disabled="(TItem.disabled || !TItem.amount) || CAC.TrnxDtlMode=='EDIT'" ng-click="CTC.addTrnx(TItem.id)">
												<div class="action-label">{{TItem.description}}</div>
												<span class="peso amount">{{TItem.amount|currency}}</span>
											</a-button>

											<!-- Transactions -->
											<a-button size="lg" class="btn-action text-center hide">

												<div class="action-label">
													Scan or Tap  ID
												</div>
												<a-glyph icon="barcode"></a-glyph>
												<a-glyph icon="qrcode"></a-glyph>
												<a-glyph icon="list-alt"></a-glyph>
											</a-button>
										</div>
								</uib-tab>
								<uib-tab index="1" heading="Schedule" classes="btn-lg" disable="CAC.PayeeType=='OTH'">

									<div class="empty-state" ng-hide="CAC.ActiveStudent.id">
										<div class="well text-center">
											<div class="empty-state-icon text-center">
												<i class="glyphicon glyphicon-calendar"></i>
											</div>
											<h4>Select Student to view payment schedule.</h4>
											<p>Search by Name, Student Number or tap their ID.</p>
										</div>
									</div>
									<div ng-show="CAC.ActiveStudent.id">
										<div class="text-center"  ng-show="hasMultiplePS" style="margin-bottom:15px;">
											<a-navpill  ng-model="CAC.PSType" options="CAC.PSTypes" type="button" ></a-navpill>
										</div>
										<a-table headers="CAC.PSHeaders" props="CAC.PSProps" data="CAC.Paysched" ></a-table>
									</div>
								</uib-tab>

								<uib-tab index="2" heading="Statement" classes="btn-lg" disable="CAC.PayeeType=='OTH'">
									<div style="height: 500px;" ng-show="CAC.ActiveStudent.id" >
										<div class="text-center"  ng-show="hasMultiplePS" style="margin-bottom:15px;">
											<a-navpill  ng-model="CAC.PSType" options="CAC.PSTypes" type="button" ></a-navpill>
										</div>
										<form action="api/reports/statement" method="GET" target="PPSOA2frame2" id="PrintPPSoa2">
											<input type="hidden" name="account_id" value="{{CAC.ActiveStudent.id}}"/>
											<input type="hidden" name="sy" value="{{CAC.ActiveSY}}"/>
											<input type="hidden" name="type" value="{{__PSType=='old'?'old':'current'}}"/>
											<input type="hidden" name="bill_month" value="{{CAC.BillMonth}}"/>
										</form>

										<form action="api/reports/statement" method="GET" target="PPSOA2frame2" id="PrintSOAPreview">
											<input type="hidden" name="account_id" value="{{CAC.ActiveStudent.id}}"/>
											<input type="hidden" name="sy" value="{{CAC.ActiveSY}}"/>
											<input type="hidden" name="type" value="current"/>
											<input type="hidden" name="bill_month" value="{{CAC.BillMonth}}"/>
											<!-- Pass precomputed payment schedule if available -->
											<input type="hidden" name="paysched" value="{{CAC.PayschedJSON}}" ng-if="CAC.PayschedJSON"/>
											<!-- Pass incoming level if SY 2025 -->
											<input type="hidden" name="incoming_level" value="{{CAC.IncomingLevelName}}" ng-if="CAC.IncomingLevelName"/>
											<!-- Pass incoming level code if SY 2025 -->
											<input type="hidden" name="incoming_level_code" value="{{CAC.IncomingLevel}}" ng-if="CAC.IncomingLevel"/>
											<!-- Pass ledger JSON with fee breakdown -->
											<input type="hidden" name="ledger" value="{{CAC.LedgerJSON}}" ng-if="CAC.LedgerJSON"/>
										</form>
										<iframe name="PPSOA2frame2"  id="PPSOA2frame" frameborder="0" style="width:100%;height:100%;display:block;"></iframe>
									</div>

									<div class="empty-state" ng-hide="CAC.ActiveStudent.id">
										<div class="well text-center">
											<div class="empty-state-icon text-center">
												<i class="glyphicon glyphicon-list-alt"></i>
											</div>
											<h4>Select Student to view Statement of Account</h4>
											<p>Search by Name, Student Number or tap their ID.</p>
										</div>
									</div>
								</uib-tab>
							</uib-tabset>
						</a-col>
						<a-col size="4">
							<div >
								<div style="margin-bottom:10px;">
									<h5 class="pull-left">Transaction Details</h5>
									<div class="pull-right" ng-show="CAC.TrnxDtlMode!='EDIT'">
										<a-button ng-click="CAC.clearTrnxDetails()" ng-disabled="!CAC.allowClear">
												Clear
										</a-button>
										<a-button type="active" ng-click="CAC.editTrnxDetails()" ng-disabled="!CAC.allowEdit">
												&nbsp;Edit&nbsp;
										</a-button>
									</div>

									<div class="pull-right"ng-show="CAC.TrnxDtlMode=='EDIT'">
										<a-button ng-click="CAC.closeTrnxDetails()">
												Close
										</a-button>
										<a-button type="primary" ng-click="CAC.updateTrnxDetails()" ng-disabled="!CAC.allowUpdate">
												Update
										</a-button>
									</div>
									<div class="clearfix"></div>
								</div>
							</div>
							<div style="height:300px">

								<!-- Transaction Details -->
								<div ng-show="CAC.TrnxDtlMode!=='EDIT'">
									<a-table headers="CAC.Headers" props="CAC.Props" data="CAC.TransacDetails" ></a-table>
								</div>
								<div ng-show="CAC.TrnxDtlMode==='EDIT'">

									<m-table-edit headers="CAC.Headers" props="CAC.Props" data="CAC.EditTransacDetails" inputs="CAC.Inputs" on-edit-save="CAC.updateDetails"  allow-add="false" allow-del="false" ></m-table-edit>
								</div>
								<!-- Transaction Details -->

								<div class="btn-group btn-group-sm pull-right" ng-if="0" style="margin-top:-21px;">
									<a-button ng-if="true">
										<a-glyph icon="chevron-left"></a-glyph>
									</a-button>
									<a-button ng-if="true">
										<a-glyph icon="chevron-right"></a-glyph>
									</a-button>
								</div>
							</div>
							<m-formgroup ng-model="CAC.TotalDispAmount" label="Total Amount" align="right" ng-disabled="true" size="'input-lg'"></m-formgroup>
							<a-button class="btn-block" size="lg" type="success" ng-click="CAC.openPaymentModal()" ng-disabled="!CAC.allowPay">
								Pay
							</a-button>

						</a-col>
					</a-row>


				</a-content>
			</a-canvas>
		</a-container>
	</a-module>

	<div ng-controller="CashierModalController as CMC" ng-init="CMC.init()">
		<a-modal id="CashierPaymentModal" title="Payment">
			<a-modal-body>
				<a-row>
					<a-col size="4">
						<m-formgroup ng-model="CMC.PayObj.doc_type" label="Doc Type" options="CMC.DocTypes"></m-formgroup>
					</a-col>
					<a-col size="4">
						<m-formgroup ng-model="CMC.PayObj.transac_date" label="Date" type="'date'"></m-formgroup>
					</a-col>
					<a-col size="4" align="right">
						<m-formgroup ng-model="CMC.PayObj.series_no" label="Series No" size="'input-lg'" ng-disabled="CMC.isCheckingSeries" ></m-formgroup>
					</a-col>
				</a-row>

				<a-row ng-show="CMC.PayObj.account_type=='student'">
					<a-col size="8">
						<m-formgroup ng-model="CMC.PayObj.student" label="Student" ng-disabled="true"></m-formgroup>
					</a-col>
					<a-col size="4">
						<m-formgroup ng-model="CMC.PayObj.section" label="Level / Section" ng-disabled="true"></m-formgroup>
					</a-col>
				</a-row>
				<a-row ng-show="CMC.PayObj.account_type=='others'">
					<a-col>
						<m-formgroup ng-model="CMC.PayObj.other" label="Account Name" ng-disabled="true"></m-formgroup>
					</a-col>
				</a-row>
				<a-row>
					<a-col size="8">
						<m-formgroup ng-model="CMC.PayObj.pay_type" label="Payment Type"  type="'btn-group'" options="CMC.PayTypes"></m-formgroup>
					</a-col>
					<a-col size="4" align="right">
						<m-formgroup ng-model="CMC.PayObj.pay_display" label="Amount" type="'display'" size="'input-lg'"></m-formgroup>
					</a-col>
				</a-row>


				<a-row>
					<a-col size="8">
						<m-formgroup ng-model="CMC.PayObj.pay_details" label="Check Details (Bank - Check No)" ng-show="CMC.PayObj.pay_type=='CHCK'"></m-formgroup>
						<m-formgroup ng-model="CMC.PayObj.pay_details" label="Card Details" ng-show="CMC.PayObj.pay_type=='CARD'"></m-formgroup>

						<m-formgroup ng-model="CMC.PayObj.pay_details" label="Online Payment Details" ng-show="CMC.PayObj.pay_type!=='CASH' && CMC.PayObj.pay_type!=='CHCK'"></m-formgroup>
					</a-col>
					<a-col size="4" align="right">
						<m-formgroup ng-model="CMC.PayObj.pay_change" label="Change" type="'number'"  ng-disabled="true"  ng-show="CMC.PayObj.pay_amount >CMC.PayObj.pay_due"></m-formgroup>
						<m-formgroup ng-model="CMC.PayObj.pay_date" label="Check Date"  type="'date'" ng-show="CMC.PayObj.pay_type=='CHCK'"></m-formgroup>

						<m-formgroup ng-model="CMC.PayObj.pay_date" label="Payment Date"  type="'date'" ng-show="CMC.PayObj.pay_type!=='CASH' && CMC.PayObj.pay_type!=='CHCK'"></m-formgroup>

					</a-col>
				</a-row>
				<a-row ng-show="CMC.isCheckingSeries">
					<a-col align="center">
						<div class="alert alert-info" style="margin-bottom:-5px;">
							<a-glyph icon="refresh animate spin"></a-glyph>
							Checking booklet, please wait.
						</div>
					</a-col>
				</a-row>
			</a-modal-body>
			<a-modal-footer>
				<a-row>
					<a-col>
						<a-button opt-class="pull-left" ng-click="CMC.closeModal()">Close</a-button>
						<a-button type="primary"ng-click="CMC.confirmPayment()" ng-disabled="!CMC.isPayObjValid">
							Confirm
					</a-button>
					</a-col>
				</a-row>

			</a-modal-footer>
		</a-modal>
	</div>

	<form action="api/receipts/cash_ar" method="POST" target="_blank" id="PrintARPayment">
		<input type="hidden" name="details" value="{{CAC.PrintPaymentDetails}}"/>
	</form>
	<form action="api/receipts/cash_a2o" method="POST" target="_blank" id="PrintA2OPayment">
		<input type="hidden" name="details" value="{{CAC.PrintPaymentDetails}}"/>
	</form>
	<form action="api/receipts/cash_or" method="POST" target="_blank" id="PrintORPayment">
		<input type="hidden" name="details" value="{{CAC.PrintPaymentDetails}}"/>
	</form>
</div>